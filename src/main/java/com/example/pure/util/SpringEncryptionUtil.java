package com.example.pure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.security.crypto.encrypt.BytesEncryptor;
import org.springframework.security.crypto.keygen.KeyGenerators;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Spring加密工具类
 * <p>
 * 使用Spring Security的加密器进行API密钥加密和解密
 * </p>
 */
@Slf4j
@Component
public class SpringEncryptionUtil {

    @Value("${app.encryption.password:defaultPassword123}")
    private String encryptionPassword;

    @Value("${app.encryption.salt:defaultSalt456}") // salt就是计算哈希值前给原始密码添加的额外密码值,为了让哈希值更安全
    private String encryptionSalt;                  // 例如12345678abc的计算哈希的值和12345678加salt值abc计算的哈希相等

    private TextEncryptor textEncryptor;           // 现有API密钥加密 (CBC)
    private BytesEncryptor compatibleKeyEncryptor; // 兼容密钥加密 (GCM)(需要认证加密,抗篡改,完整性验证用)
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    public SpringEncryptionUtil(SnowflakeIdGenerator snowflakeIdGenerator) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
    }

    @PostConstruct
    public void init() {
        try {
            // 现有的CBC模式加密器（向后兼容）
            this.textEncryptor = Encryptors.text(encryptionPassword, encryptionSalt);

            // 新的GCM模式加密器（用于兼容密钥）
            String gcmSalt = generateGcmSalt(encryptionSalt);
            this.compatibleKeyEncryptor = Encryptors.stronger(encryptionPassword, gcmSalt);

            log.info("Spring加密工具初始化成功（CBC + GCM模式）");
        } catch (Exception e) {
            log.error("Spring加密工具初始化失败", e);
            throw new RuntimeException("加密工具初始化失败", e);
        }
    }

    /**
     * 生成GCM专用盐值
     * 使用Spring Security的KeyGenerators生成安全的盐值
     */
    private String generateGcmSalt(String originalSalt) {
        try {
            // 使用Spring Security的安全随机盐值生成器
            String randomSalt = KeyGenerators.string().generateKey();

            // 获取SHA256哈希算法对象
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            String combinedSalt = originalSalt + "gcm" + randomSalt;
            // 对输入数据进行SHA256哈希计算
            byte[] hash = digest.digest(combinedSalt.getBytes(StandardCharsets.UTF_8));

            return bytesToHex(hash).substring(0, 32); // 取前32个字符作为新盐值
        } catch (Exception e) {
            throw new RuntimeException("生成GCM盐值失败", e);
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 加密API密钥
     *
     * @param apiKey 原始API密钥
     * @return 加密后的API密钥
     */
    public String encrypt(String apiKey) {
        try {
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("API密钥不能为空");
            }
            return textEncryptor.encrypt(apiKey);
        } catch (Exception e) {
            log.error("加密API密钥失败", e);
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 解密API密钥
     *
     * @param encryptedApiKey 加密后的API密钥
     * @return 原始API密钥
     */
    public String decrypt(String encryptedApiKey) {
        try {
            if (encryptedApiKey == null || encryptedApiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("加密的API密钥不能为空");
            }
            return textEncryptor.decrypt(encryptedApiKey);
        } catch (Exception e) {
            log.error("解密API密钥失败", e);
            throw new RuntimeException("解密失败", e);
        }
    }



    /**
     * 生成OpenAI格式的兼容API密钥（使用Spring Crypto GCM）
     * 格式：sk-{spring_crypto_encrypted_data}
     *
     * @param userId 用户ID
     * @param keyName 密钥名称
     * @return 兼容格式的API密钥
     */
    public CompatibleKeyResult generateCompatibleApiKey(Long userId, String keyName) {
        try {
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID不能为空或小于等于0");
            }
            if (keyName == null || keyName.trim().isEmpty()) {
                keyName = "default";
            }

            // 构造明文数据：userId|timestamp|keyName|random
            String plaintext = String.format("%d|%d|%s|%d",
                userId,
                System.currentTimeMillis(),
                keyName.trim(),
                ThreadLocalRandom.current().nextLong()
            );

            // 使用Spring Crypto GCM加密 - BytesEncryptor
            byte[] encryptedBytes = compatibleKeyEncryptor.encrypt(plaintext.getBytes(StandardCharsets.UTF_8));
            String encrypted = Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedBytes);
            String compatibleKey = "sk-" + encrypted;

            // 生成安全哈希用于数据库存储验证
            String securityHash = generateHash(plaintext);
            Long snowflakeId = snowflakeIdGenerator.nextId();

            log.debug("生成兼容API密钥成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return new CompatibleKeyResult(snowflakeId, compatibleKey, securityHash);
        } catch (Exception e) {
            log.error("生成兼容API密钥失败 - 用户ID: {}, 密钥名称: {}", userId, keyName, e);
            throw new RuntimeException("生成兼容API密钥失败: " + e.getMessage(), e);
        }
    }



    /**
     * 解析兼容API密钥（使用Spring Crypto GCM）
     * 从密钥中提取用户信息，用于负载均衡
     *
     * @param compatibleKey 兼容密钥
     * @return 解析结果
     */
    public ParseResultV2 parseCompatibleApiKey(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return new ParseResultV2(false, null, null, null, "密钥格式无效");
            }

            String encrypted = compatibleKey.substring(3); // 去掉"sk-"

            // 使用Spring Crypto GCM解密 - BytesEncryptor
            byte[] encryptedBytes = Base64.getUrlDecoder().decode(encrypted);
            byte[] decryptedBytes = compatibleKeyEncryptor.decrypt(encryptedBytes);
            String plaintext = new String(decryptedBytes, StandardCharsets.UTF_8);
            String[] parts = plaintext.split("\\|");

            if (parts.length != 4) {
                return new ParseResultV2(false, null, null, null, "密钥数据格式无效");
            }

            Long userId = Long.parseLong(parts[0]);
            Long timestamp = Long.parseLong(parts[1]);
            String keyName = parts[2];

            // 验证时间戳（密钥有效期：1年）
            long currentTime = System.currentTimeMillis();
            if (currentTime - timestamp > 365L * 24 * 60 * 60 * 1000) {
                return new ParseResultV2(false, userId, timestamp, keyName, "密钥已过期");
            }

            log.debug("解析兼容API密钥成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return new ParseResultV2(true, userId, timestamp, keyName, "解析成功");
        } catch (Exception e) {
            log.warn("解析兼容API密钥失败: {}", compatibleKey, e);
            return new ParseResultV2(false, null, null, null, "解析失败: " + e.getMessage());
        }
    }





    /**
     * 验证兼容密钥
     */
    public boolean validateCompatibleKey(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return false;
            }

            ParseResultV2 result = parseCompatibleApiKey(compatibleKey);
            return result.isValid();
        } catch (Exception e) {
            log.warn("验证兼容密钥失败: {}", compatibleKey, e);
            return false;
        }
    }



    /**
     * 生成哈希值
     */
    private String generateHash(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes).replaceAll("[^a-zA-Z0-9]", "");
    }

    /**
     * 脱敏显示API密钥
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }

        if (apiKey.startsWith("sk-")) {
            // OpenAI格式密钥脱敏
            return apiKey.substring(0, 7) + "****" + apiKey.substring(apiKey.length() - 4);
        } else {
            // 其他格式密钥脱敏
            return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
        }
    }

    /**
     * 兼容密钥生成结果
     */
    public static class CompatibleKeyResult {
        private final Long snowflakeId;
        private final String compatibleKey;
        private final String securityHash;

        public CompatibleKeyResult(Long snowflakeId, String compatibleKey, String securityHash) {
            this.snowflakeId = snowflakeId;
            this.compatibleKey = compatibleKey;
            this.securityHash = securityHash;
        }

        public Long getSnowflakeId() { return snowflakeId; }
        public String getCompatibleKey() { return compatibleKey; }
        public String getSecurityHash() { return securityHash; }
    }

    /**
     * 解析结果（支持密钥名称和错误信息）
     */
    public static class ParseResultV2 {
        private final boolean valid;
        private final Long userId;
        private final Long timestamp;
        private final String keyName;
        private final String message;

        public ParseResultV2(boolean valid, Long userId, Long timestamp, String keyName, String message) {
            this.valid = valid;
            this.userId = userId;
            this.timestamp = timestamp;
            this.keyName = keyName;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public Long getTimestamp() { return timestamp; }
        public String getKeyName() { return keyName; }
        public String getMessage() { return message; }
    }
}
